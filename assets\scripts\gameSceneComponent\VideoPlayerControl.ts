const { ccclass, property } = cc._decorator;

import { globalVariables } from '../utils/GlobalVariables';
import Request from '../apis/api';
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  VideoPlayer: cc.Node = null;

  // @property(cc.Node)
  // maskSuccess: cc.Node = null;

  onLoad() {
    // this.VideoPlayer.on('ready-to-play', this.onVideoPlayerEvent, this);
    // this.scheduleOnce(() => {
    //   let videos = document.getElementsByTagName('video');
    //   if (videos.length > 0) {
    //     let v = videos[0];
    //     v.style.position = 'absolute';
    //     v.style.zIndex = '9999'; // 确保在前面（按需调整）
    //     v.style.left = '50%';
    //     v.style.top = '50%';
    //     v.style.transform = 'translate(-50%, -50%) !important';
    //     v.style.transformOrigin = 'center center !important';
    //     v.style.width = '100vw'; // 占满屏幕宽
    //     v.style.height = 'auto'; // 按比例自适应高
    //     v.style.maxHeight = '100vh'; // 防止高度超出屏幕3
    //   }
    // }, 1);

    console.log('Position:', this.VideoPlayer.position);
    console.log('AnchorPoint:', this.VideoPlayer.getAnchorPoint());
    cc.log('Content size:', this.VideoPlayer.getContentSize());
  }
  onVideoPlayerEvent(player, eventType, customEventData) {
    cc.log('videoplayer', player);
    cc.log('eventType', eventType);

    if (eventType === cc.VideoPlayer.EventType.READY_TO_PLAY) {
      // 若视频准备好了，这个事件并不保障会在所有平台或浏览器中被触发，它依赖于平台实现，请不要依赖于这个事件做视频播放的控制。
      cc.log('视频准备播放了');

      // this.scheduleOnce(() => {
      //   player.play();
      // }, 500);
      
      let videos = document.getElementsByTagName('video');
      if (videos.length > 0) {
        let v = videos[0];
        v.style.position = 'absolute';
        v.style.zIndex = '9999'; // 确保在前面（按需调整）
        v.style.left = '50%';
        v.style.top = '50%';
        v.style.transform = 'translate(-50%, -50%) !important';
        v.style.transformOrigin = 'center center !important';
        v.style.width = '100vw'; // 占满屏幕宽
        v.style.height = 'auto'; // 按比例自适应高
        v.style.maxHeight = '100vh'; // 防止高度超出屏幕3
      }
    } else if (eventType === cc.VideoPlayer.EventType.PLAYING) {
      // 若视频正在播放,do something...
      cc.log('视频正在播放');
    } else if (eventType === cc.VideoPlayer.EventType.PAUSED) {
      // 若视频暂停, do something...
      cc.log('视频暂停了');
    } else if (eventType === cc.VideoPlayer.EventType.STOPPED) {
      // 若视频停止, do something...
      cc.log('视频停止了');
    } else if (eventType === cc.VideoPlayer.EventType.COMPLETED) {
      // 若播放结束,do something...
      cc.log('视频的元信息已加载完成');
    } else if (eventType === cc.VideoPlayer.EventType.META_LOADED) {
      // 若视频的元信息已加载完成，你可以调用 getDuration 来获取视频总时长1
      cc.log('视频元信息已加载完成');
    } else if (eventType === cc.VideoPlayer.EventType.CLICKED) {
      // 若点击了视频, do something...
      // 勾选了StayOnBottom后这个不能用
      cc.log('点击了视频');
    }
    //这里 videoplayer 是一个 VideoPlayer 组件对象实例
    // 这里的 eventType === cc.VideoPlayer.EventType enum 里面的值
    //这里的 customEventData 参数就等于你之前设置的 "foobar"
  }
  onDestroy() {
    // this.VideoPlayer.off('ready-to-play', this.onVideoPlayerEvent, this);
  }
}
