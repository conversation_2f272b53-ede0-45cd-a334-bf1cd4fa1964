// 全局变量

import { getImageURL } from './generalUtil';
class GlobalVariables {
  public loggedIn: boolean = false;
  public userInfo: any = null;
  public userUUID: string = ''; // 用户唯一标识UUID
  public shareInfo: any = {
    title: '“慧眼寻安”找茬挑战小游戏',
    desc: '安全隐患藏不住“慧眼寻安”找茬挑战，第3关我就卡住了…你能过几关？',
    imgUrl: getImageURL('FibsI8ITZipShQkQ-Ywpva-The4e'),
    link: `https://${
      CC_DEBUG ? 'dev' : 'radio'
    }.jgrm.net/actions/game/highway/index.html`,
  };
  public showMarkBlack: boolean = true;
  public hasShook: boolean = false; // 是否摇了签筒
  public fortuneSticksNum: number = null; // 抽中的幸运签
  public currentLevel: number = 3;
  public currentFoundElement: number = 0;
  public currentFoundElementIndexArray: Array<number> = [
    0, 0, 0, 0, 0, 0, 0, 0,
  ];
  public isFirstLoadGame: boolean = true;
  public ifInitCloudData: boolean = false;
  public LevelShouldFoundElementArray: Array<number> = [3, 5, 8];
  public passLevelArray: Array<number> = [1, 1, 0];
  public passTimeArray: Array<number> = [-1, -1, -1];
  public openId: string = '';
  public drawsNum: number = 3;
  public awardsArray: Array<number> = [0, 0, 0, 0, 0];
  public checkInArray: Array<number> = [
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0,
  ];
  public LevelElementData: Array<
    Array<{
      resourcesURL: string;
      position: [number, number];
      size?: [number, number];
      explainPosition?: [number, number];
    }>
  > = [
    [
      {
        resourcesURL: 'coreGameSceneMaterial/image/level1/h_car',
        position: [-21, -1768],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level1/h_car1',
        position: [-94, -3848],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level1/h_xingren',
        position: [40, -4976],
      },
    ],
    [
      {
        resourcesURL: 'coreGameSceneMaterial/image/level2/h2_hulan',
        position: [164, -478],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level2/h2_zw',
        position: [-155, -1133],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level2/h2_zsp',
        position: [186, -1900],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level2/h2_car',
        position: [15, -3204],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level2/h2_deng',
        position: [147, -4409],
      },
    ],
    [
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_jk',
        position: [117, -300],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_ssf',
        position: [-59, -811],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_xsp',
        position: [87, -1326],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_hl',
        position: [62, -2601],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_sg',
        position: [-95, -3397],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_aqck',
        position: [67, -4422],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_pdx',
        position: [37, -5351],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level3/h3_mhq',
        position: [-130, -5502],
      },
    ],
    [
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/phone',
        position: [-68, -400],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/highwayParking',
        position: [-71, -1104],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/emergencyPassage',
        position: [11, -1915],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/fatigueDriving',
        position: [-76, -2768],
      },
      {
        resourcesURL: 'coreGameSceneMaterial/image/level4/drinkDrive',
        position: [-94, -3657],
      },
    ],
  ];
  // 添加LevelKnowledgeData类型定义
  public LevelKnowledgeData: Array<
    Array<{
      content: string;
      tips: string;
      tipsPosition?: [number, number];
    }>
  > = [
    [
      {
        content: '',
        tips: '货物掉落未及时清理',
      },
      {
        content: '',
        tips: '未按规定放置警示标志的故障车辆',
      },
      {
        content: '',
        tips: '行人违规进入高速公路',
      },
    ],
    [
      {
        content: '',
        tips: '高速公路防护栏破损未修复',
        tipsPosition: [-41, 0],
      },
      {
        content: '',
        tips: '路面出现坑洼未填补',
      },
      {
        content: '',
        tips: '交通标志牌被遮挡',
        tipsPosition: [-41, 0],
      },
      {
        content: '',
        tips: '车辆违规占用应急车道',
      },
      {
        content: '',
        tips: '隧道内照明故障',
        tipsPosition: [-41, 0],
      },
      {
        content:
          '消防栓是火灾扑救时的重要供水设备，一旦破损漏水，在火灾发生时可能无法正常供水或水压不足，导致消防水枪无法有效喷射，消防人员难以快速控制火势，使火灾蔓延扩大，增加人员伤亡和财产损失的风险。漏水可能会使附近的电气设备、线路等受潮，引发短路、漏电等电气故障，不仅会损坏电气设备，还可能导致人员触电伤亡，进一步扩大安全事故范围。《中华人民共和国消防法》：规定了单位和个人有维护消防安全、保护消防设施的义务。对损坏、挪用或者擅自拆除、停用消防设施、器材的行为，单位将面临五千元以上五万元以下罚款，个人处警告或者五百元以下罚款。',
        tips: '消防栓破损漏水安全隐患',
        // tipsPosition: [-157, 34],
      },
    ],

    [
      {
        content: '',
        tips: '道路监控控制箱门遗失',
      },
      {
        content: '',
        tips: '伸缩缝有杂物',
      },
      {
        content: '',
        tips: '危险品车辆限速标识倒伏',
      },
      {
        content: '',
        tips: '波形护拦板断裂',
        // tipsPosition: [90, -47],
      },
      {
        content: '',
        tips: '施工区域警示设置不规范',
        // tipsPosition: [-47, -53],
      },
      {
        content: '',
        tips: '安全出口指示灯故障',
      },
      {
        content: '',
        tips: '配电箱有积水渗入',
        // tipsPosition: [-47, -57],
      },
      {
        content: '',
        tips: '灭火器压力不足或失效',
        // tipsPosition: [-47, -57],
      },
    ],
    [
      {
        content:
          '驾驶机动车中玩手机会造成注意力严重分散，反应速度减低，视野范围变窄，车辆操控不稳等风险，河南应急广播提醒您驾驶机动车期间不要玩手机等电子产品。《中华人民共和国道路交通安全法》：第九十条规定，机动车驾驶人违反道路交通安全法律、法规关于道路通行规定的，处警告或者二十元以上二百元以下罚款。若因开车玩手机引发交通事故，造成人身或财产损失等更严重后果，需承担相应民事赔偿责任，甚至可能涉及刑事处罚。《中华人民共和国道路交通安全法实施条例》：第六十二条明确规定，驾驶机动车不得有拨打接听手持电话、观看电视等妨碍安全驾驶的行为。',
        tips: '驾驶中玩手机',
      },
      {
        content:
          '车辆在道路上发生故障，具备移至应急车道的条件却未移至应急车道，而是直接在行车道等非应急车道区域停车维修，就可认定为违法停车或不按规定使用应急车道。即使车辆故障无法移动，若未按规定开启危险报警闪光灯并设置警告标志。车辆在道路上发生故障，具备移至应急车道的条件却未移至应急车道，而是直接在行车道等非应急车道区域停车维修，就可认定为违法停车或不按规定使用应急车道。即使车辆故障无法移动，若未按规定开启危险报警闪光灯并设置警告标志。《中华人民共和国道路交通安全法》：第五十二条规定，机动车在道路上发生故障，需要停车排除故障时，驾驶人应当立即开启危险报警闪光灯，将机动车移至不妨碍交通的地方停放；难以移动的，应当持续开启危险报警闪光灯，并在来车方向设置警告标志等措施扩大示警距离，必要时迅速报警。第九十条规定，机动车驾驶人违反道路交通安全法律、法规关于道路通行规定的，处警告或者二十元以上二百元以下罚款。',
        tips: '违法停车',
      },
      {
        content:
          '非紧急情况下，社会车辆在应急车道上行驶或停车，即构成违法占用应急车道。紧急情况一般指车辆发生故障无法正常上路行驶，如爆胎、制动失灵等；车内人员突发危及生命的疾病；在交警授权的情况下，如前方发生交通事故、占用行车道，车辆必须从应急车道行驶才能通过；以及工程救险、消防救援、医疗救护以及民警执行紧急公务等。《中华人民共和国道路交通安全法实施条例》：第八十二条第四项规定，机动车在高速公路上行驶，不得有非紧急情况时在应急车道行驶或者停车的行为。《道路交通安全违法行为记分管理办法》：第十条第十一款规定，驾驶机动车在高速公路或者城市快速路上违法占用应急车道行驶的，一次记 6 分。《中华人民共和国道路交通安全法》：第九十条规定，机动车驾驶人违反道路交通安全法律、法规关于道路通行规定的，处警告或者二十元以上二百元以下罚款。',
        tips: '违法占用应急车道',
        tipsPosition: [-60, -177],
      },
      {
        content:
          '连续驾驶机动车超过 4 小时未停车休息或者停车休息时间少于 20 分钟，即属于疲劳驾驶。《中华人民共和国道路交通安全法》：第二十二条规定，机动车驾驶人应当遵守道路交通安全法律、法规的规定，按照操作规范安全驾驶、文明驾驶。过度疲劳影响安全驾驶的，不得驾驶机动车。第九十条规定，机动车驾驶人违反道路交通安全法律、法规关于道路通行规定的，处警告或者二十元以上二百元以下罚款。本法另有规定的，依照规定处罚。《中华人民共和国道路交通安全法实施条例》：第六十二条规定，驾驶机动车不得有连续驾驶机动车超过 4 小时未停车休息或者停车休息时间少于 20 分钟的行为。',
        tips: '疲劳驾驶',
      },
      {
        content:
          '车辆驾驶人员血液中的酒精含量大于或者等于 20mg/100ml、小于 80mg/100ml 的驾驶行为属于饮酒驾驶。饮酒驾驶会造成触觉能力降低，判断能力和操作能力降低，视觉障碍，疲劳驾驶等。《中华人民共和国道路交通安全法》第九十一条规定：饮酒后驾驶机动车的，处暂扣六个月机动车驾驶证，并处一千元以上二千元以下罚款。因饮酒后驾驶机动车被处罚，再次饮酒后驾驶机动车的，处十日以下拘留，并处一千元以上二千元以下罚款，吊销机动车驾驶证。饮酒后驾驶营运机动车的，处十五日拘留，并处五千元罚款，吊销机动车驾驶证，五年内不得重新取得机动车驾驶证。',
        tips: '饮酒驾驶',
        tipsPosition: [-47, -57],
      },
    ],
  ];
}
export let globalVariables: GlobalVariables = new GlobalVariables();
